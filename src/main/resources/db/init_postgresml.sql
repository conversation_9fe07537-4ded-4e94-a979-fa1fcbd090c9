-- PostgresML 向量数据库初始化脚本
-- 用于K12教育RAG AI聊天项目
-- 支持向量检索和教育内容分类存储
-- 向量维度：1536（与embedding模型维度一致）

-- 0. 清理旧表和函数
DROP TABLE IF EXISTS math_rag_knowledge CASCADE;
DROP TABLE IF EXISTS embeddings CASCADE;
DROP FUNCTION IF EXISTS search_knowledge CASCADE;
DROP FUNCTION IF EXISTS add_knowledge CASCADE;
DROP FUNCTION IF EXISTS search_embeddings CASCADE;
DROP FUNCTION IF EXISTS add_embedding CASCADE;

-- 1. 创建 pgvector 扩展（如果还没有安装）
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 创建K12教育知识库表（1536维向量）
CREATE TABLE IF NOT EXISTS math_rag_knowledge (
    id SERIAL PRIMARY KEY,
    source TEXT,                    -- 数据来源（PDF名称、网页URL等）
    type TEXT,                      -- 内容类型：problem/concept/example/definition/solution
    content TEXT NOT NULL,          -- 主体文本（题干/定义/讲解）
    answer TEXT,                    -- 答案或解答摘要（如果有）
    latex TEXT,                     -- LaTeX表达式（用于渲染公式）
    sympy_expr TEXT,                -- 可计算的表达式（用于验证答案）
    graph TEXT,                     -- 图形文件路径或SVG代码
    topic TEXT,                     -- 知识点（Algebra, Geometry, Statistics等）
    grade TEXT,                     -- 年级（P5, Sec2, O-Level等）
    difficulty TEXT,                -- 难度：基础/中等/挑战
    metadata JSONB,                 -- 额外信息（章节/页码/年份/标签等）
    embedding vector(1536),         -- 向量表示（用于检索）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量相似度搜索索引（HNSW索引）
CREATE INDEX IF NOT EXISTS math_rag_knowledge_embedding_idx
ON math_rag_knowledge USING hnsw (embedding vector_cosine_ops);


-- 3. 创建其他索引
-- 创建元数据索引
CREATE INDEX IF NOT EXISTS math_rag_knowledge_metadata_idx
ON math_rag_knowledge USING GIN (metadata);

-- 创建教育相关字段索引
CREATE INDEX IF NOT EXISTS math_rag_knowledge_grade_idx
ON math_rag_knowledge (grade);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_topic_idx
ON math_rag_knowledge (topic);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_type_idx
ON math_rag_knowledge (type);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_difficulty_idx
ON math_rag_knowledge (difficulty);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_source_idx
ON math_rag_knowledge (source);

-- 为新增字段创建索引（用于快速查找有答案、有公式、有图形的内容）
CREATE INDEX IF NOT EXISTS math_rag_knowledge_answer_idx
ON math_rag_knowledge (answer) WHERE answer IS NOT NULL;

CREATE INDEX IF NOT EXISTS math_rag_knowledge_latex_idx
ON math_rag_knowledge (latex) WHERE latex IS NOT NULL;

CREATE INDEX IF NOT EXISTS math_rag_knowledge_graph_idx
ON math_rag_knowledge (graph) WHERE graph IS NOT NULL;

-- 4. 创建知识库相似度搜索函数（1536维）
CREATE OR REPLACE FUNCTION search_knowledge(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.75,
    match_count int DEFAULT 5,
    filter_grade text DEFAULT NULL,
    filter_topic text DEFAULT NULL,
    filter_type text DEFAULT NULL,
    filter_difficulty text DEFAULT NULL
)
RETURNS TABLE(
    id int,
    source text,
    type text,
    content text,
    answer text,
    latex text,
    sympy_expr text,
    graph text,
    topic text,
    grade text,
    difficulty text,
    metadata jsonb,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        k.id,
        k.source,
        k.type,
        k.content,
        k.answer,
        k.latex,
        k.sympy_expr,
        k.graph,
        k.topic,
        k.grade,
        k.difficulty,
        k.metadata,
        1 - (k.embedding <=> query_embedding) AS similarity
    FROM math_rag_knowledge k
    WHERE 1 - (k.embedding <=> query_embedding) > match_threshold
        AND (filter_grade IS NULL OR k.grade = filter_grade)
        AND (filter_topic IS NULL OR k.topic = filter_topic)
        AND (filter_type IS NULL OR k.type = filter_type)
        AND (filter_difficulty IS NULL OR k.difficulty = filter_difficulty)
    ORDER BY k.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 5. 创建添加知识的函数（1536维）
CREATE OR REPLACE FUNCTION add_knowledge(
    p_source text,
    p_type text,
    p_content text,
    p_answer text DEFAULT NULL,
    p_latex text DEFAULT NULL,
    p_sympy_expr text DEFAULT NULL,
    p_graph text DEFAULT NULL,
    p_topic text,
    p_grade text,
    p_difficulty text,
    p_embedding vector(1536),
    p_metadata jsonb DEFAULT NULL
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    new_id int;
BEGIN
    INSERT INTO math_rag_knowledge (source, type, content, answer, latex, sympy_expr, graph, topic, grade, difficulty, embedding, metadata)
    VALUES (p_source, p_type, p_content, p_answer, p_latex, p_sympy_expr, p_graph, p_topic, p_grade, p_difficulty, p_embedding, p_metadata)
    RETURNING id INTO new_id;

    RETURN new_id;
END;
$$;

-- 6. 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_math_rag_knowledge_updated_at
    BEFORE UPDATE ON math_rag_knowledge
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 验证配置信息
SELECT 'K12教育RAG AI聊天项目数据库初始化完成' as status;
SELECT '支持向量检索和教育内容分类存储' as description;
SELECT '向量维度: 1536' as dimension;

-- 显示表结构和索引信息
\d math_rag_knowledge
\di math_rag_knowledge*;

-- 8. 示例查询（注释掉，仅供参考）
/*
-- 示例：搜索中学二年级代数相关内容
SELECT id, type, content, answer, latex, sympy_expr, graph, topic, grade, difficulty, metadata, similarity
FROM search_knowledge(
    '[query_embedding]'::vector(1536),
    0.75,
    5,
    'Sec2',     -- 年级过滤
    'Algebra',  -- 主题过滤
    'problem',  -- 类型：题目
    NULL        -- 难度不限
);

-- 示例：添加新的知识条目（代数题）
SELECT add_knowledge(
    'PSLE 2020 Paper 1 Q5',                    -- source
    'problem',                                  -- type
    'Solve 2x + 3 = 7',                       -- content
    'x = 2',                                   -- answer
    '2x + 3 = 7 \\Rightarrow x = 2',          -- latex
    'Eq(2*x + 3, 7)',                         -- sympy_expr
    NULL,                                      -- graph (无图形)
    'Algebra',                                 -- topic
    'Sec2',                                    -- grade
    '基础',                                    -- difficulty
    '[embedding_vector]'::vector(1536),       -- embedding
    '{"chapter": "Linear Equations", "page": 12, "tags": ["equation", "solving"]}'::jsonb -- metadata
);

-- 示例：添加几何题（带图形）
SELECT add_knowledge(
    'PSLE 2021 Paper 2 Q12',                  -- source
    'problem',                                 -- type
    'Find the area of the shaded triangle in rectangle ABCD, where AE = 4 cm, EB = 8 cm, and DC = 6 cm.', -- content
    '36 cm^2',                                 -- answer
    'Area = \\tfrac{1}{2} \\times (AE + EB) \\times DC = 36 \\, cm^2', -- latex
    'Rational(1,2) * (4+8) * 6',              -- sympy_expr
    'triangle_area_2021_q12.svg',             -- graph
    'Geometry',                                -- topic
    'P5',                                      -- grade
    '中等',                                    -- difficulty
    '[embedding_vector]'::vector(1536),       -- embedding
    '{"chapter": "Triangle Area", "page": 45, "tags": ["triangle", "area"]}'::jsonb -- metadata
);

-- 示例：添加概念定义（无答案）
SELECT add_knowledge(
    'Textbook P5 Chapter 3',                  -- source
    'concept',                                 -- type
    'Height of a triangle is the perpendicular distance from a vertex to the opposite side.', -- content
    NULL,                                      -- answer (概念无答案)
    NULL,                                      -- latex (无公式)
    NULL,                                      -- sympy_expr (无计算)
    'triangle_height.svg',                     -- graph
    'Geometry',                                -- topic
    'P5',                                      -- grade
    '基础',                                    -- difficulty
    '[embedding_vector]'::vector(1536),       -- embedding
    '{"chapter": "Triangle Properties", "page": 10, "tags": ["definition", "triangle"]}'::jsonb -- metadata
);
*/